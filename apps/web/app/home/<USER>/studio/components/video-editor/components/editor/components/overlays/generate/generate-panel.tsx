"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "../../../../../components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "../../../../../components/ui/tabs";
import { Textarea } from "../../../../../components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../../../components/ui/select";
import { ScrollArea } from "../../../../../components/ui/scroll-area";
import { Card, CardContent, CardFooter } from "../../../../../components/ui/card";
import { Label } from "../../../../../components/ui/label";
import { Spinner } from '../../../../../components/ui/spinner';
// import { Input } from '../../../../../components/ui/input';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "../../../../../components/ui/collapsible";
import { ChevronDown, Edit, Save, Plus, Trash2, Video } from "lucide-react";
import { OverlayType } from "../../../types";
// import { useSidebar } from "../../../contexts/sidebar-context";
import { useEditorContext } from "../../../contexts/editor-context";

interface Scene {
  text: string;
  visualType: "STOCK" | "AI";
  visualDescription: string;
}

export default function GeneratePanel() {
  const [inputText, setInputText] = React.useState("");
  const [language, setLanguage] = React.useState("English");
  const [isLoading, setIsLoading] = React.useState(false);
  const [generatedScenes, setGeneratedScenes] = React.useState<Scene[]>([]);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState<string | null>(null);
  const [activeTabKey, setActiveTabKey] = React.useState("generate");
  const [editingSceneIndex, setEditingSceneIndex] = React.useState<number | null>(null);
  const [editingScene, setEditingScene] = React.useState<Scene | null>(null);
  const [generatingVideo, setGeneratingVideo] = React.useState(false);
  const [promoStyle, setPromoStyle] = React.useState("");
  const [toneSelections, setToneSelections] = React.useState<string[]>([]);
  const [videoLength, setVideoLength] = React.useState("");
  const [musicStyle, setMusicStyle] = React.useState("");
  const [fontStyle, setFontStyle] = React.useState("");
  
  // Access editor context directly
  const { 
    // addOverlay, 
    // resetOverlays,
    // changeOverlay,
    // setSelectedOverlayId,
    setOverlays,
    // overlays 
  } = useEditorContext();
  
  // const { setActivePanel } = useSidebar();

  const generateScript = async () => {
    if (!inputText.trim()) {
      setError("Please enter some text to generate a script");
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch("/api/latest/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: inputText,
          language,
          promoStyle,
          toneSelections,
          videoLength,
          musicStyle,
          fontStyle,
        }),
      });

      const data = await response.json();
      
      if (data.error) {
        setError(data.error);
        return;
      }

      setGeneratedScenes(data.scenes);
      setActiveTabKey("scenes");
    } catch (err) {
      setError("Failed to generate script. Please try again.");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const startEditingScene = (index: number) => {
    setEditingSceneIndex(index);
    setEditingScene({...generatedScenes[index]});
  };

  const cancelEditingScene = () => {
    setEditingSceneIndex(null);
    setEditingScene(null);
  };

  const saveEditedScene = () => {
    if (editingSceneIndex !== null && editingScene) {
      const updatedScenes = [...generatedScenes];
      updatedScenes[editingSceneIndex] = editingScene;
      setGeneratedScenes(updatedScenes);
      setEditingSceneIndex(null);
      setEditingScene(null);
    }
  };

  const addNewScene = () => {
    const newScene: Scene = {
      text: "",
      visualType: "STOCK",
      visualDescription: ""
    };
    setGeneratedScenes([...generatedScenes, newScene]);
    startEditingScene(generatedScenes.length);
  };

  const deleteScene = (index: number) => {
    const updatedScenes = [...generatedScenes];
    updatedScenes.splice(index, 1);
    setGeneratedScenes(updatedScenes);
    if (editingSceneIndex === index) {
      cancelEditingScene();
    }
  };
  
  const generateVideo = async () => {
    if (generatedScenes.length === 0) {
      setError("Please generate or add scenes first");
      return;
    }
    
    setGeneratingVideo(true);
    setError(null);
    
    try {
      // Clear the existing timeline by setting to empty array
      setOverlays([]);
      
      let currentTimeOffset = 0; // in milliseconds
      const fps = 30; // frames per second
      const captionSegments = [];
      const newOverlays = [];
      const soundPromises = [];
      
      // Process each scene to create audio and captions
      const videoSearchPromises = [];
      
      for (const scene of generatedScenes) {
        const sceneText = scene.text.trim();
        if (!sceneText) continue;
        
        // Call the text-to-speech API for this scene
        const ttsPromise = fetch("/api/latest/text-to-speech", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            text: sceneText,
            language,
          }),
        }).then(response => response.json());
        
        soundPromises.push(ttsPromise);
        
        // If it's a stock visual, search for it using Pexels API
        if (scene.visualType === "STOCK" && scene.visualDescription) {
          const videoSearchPromise = fetch(
            `/api/latest/search-stock?query=${encodeURIComponent(scene.visualDescription)}&source=pexels`
          ).then(response => response.json());
          
          videoSearchPromises.push(videoSearchPromise);
        } else {
          // Push null for non-stock scenes to maintain array index alignment
          videoSearchPromises.push(Promise.resolve(null));
        }
      }
      
      // Process all TTS and video search responses
      const [ttsResults, videoResults] = await Promise.all([
        Promise.all(soundPromises),
        Promise.all(videoSearchPromises)
      ]);
      
      // Check for errors in any of the TTS responses
      const errorResult = ttsResults.find(result => result.error);
      if (errorResult) {
        throw new Error(errorResult.error);
      }
      
      // Generate IDs for the new overlays
      let nextId = 1;
      
      // Process each TTS result into audio and caption overlays
      for (let i = 0; i < ttsResults.length; i++) {
        const ttsData = ttsResults[i];
        const scene = generatedScenes[i];
        const sceneText = scene.text.trim();
        if (!sceneText) continue;
        
        const durationMs = ttsData.duration * 1000; // duration in milliseconds
        const durationFrames = Math.ceil(durationMs / 1000 * fps);
        
        // Create sound overlay for audio
        newOverlays.push({
          id: nextId++,
          type: OverlayType.SOUND,
          content: `Generated Audio: ${sceneText.substring(0, 20)}${sceneText.length > 20 ? '...' : ''}`,
          src: `data:audio/mp3;base64,${ttsData.audioBase64}`,
          from: Math.round(currentTimeOffset / 1000 * fps), // Convert ms to frames
          durationInFrames: durationFrames,
          startFromSound: 0,
          row: 0,
          left: 0,
          top: 0,
          width: 1920,
          height: 100,
          isDragging: false,
          rotation: 0,
          styles: {
            volume: 1
          }
        });
        
        // Process words for captions using the alignment data from ElevenLabs
        // Convert character-level timestamps to word-level timestamps
        const wordTimestamps = extractWordTimestamps(ttsData, sceneText);
        
        const captionWords = wordTimestamps.map(({word, start, end}) => ({
          word,
          startMs: start * 1000 + currentTimeOffset, // Convert seconds to ms and add offset
          endMs: end * 1000 + currentTimeOffset,
          confidence: 1.0
        }));
        
        captionSegments.push({
          text: sceneText,
          startMs: currentTimeOffset,
          endMs: currentTimeOffset + durationMs,
          timestampMs: null,
          confidence: 1.0,
          words: captionWords
        });
        
        if (scene.visualType === "STOCK" && scene.visualDescription) {
          const videos = videoResults[i];
          
          if (videos && videos.length > 0) {
            // Get the first video result
            const video = videos[0];
            
            // Find the best quality video file (prioritize HD > SD)
            const videoFile =
              video.video_files.find(file => file.quality === "hd") ||
              video.video_files.find(file => file.quality === "sd") ||
              video.video_files[0]; // Fallback to first file if no matches
            
            if (videoFile) {
              // Add video overlay
              newOverlays.push({
                id: nextId++,
                type: OverlayType.VIDEO,
                content: video.image, // Thumbnail image
                src: videoFile.link, // Video URL
                from: Math.round(currentTimeOffset / 1000 * fps), // Convert ms to frames
                durationInFrames: durationFrames,
                videoStartTime: 0,
                row: 2,
                left: 0,
                top: 0,
                width: 1920,
                height: 1080,
                isDragging: false,
                rotation: 0,
                styles: {
                  opacity: 1,
                  zIndex: 100,
                  transform: "none",
                  objectFit: "cover"
                }
              });
            } else {
              // Fallback to text description if no suitable video file
              newOverlays.push({
                id: nextId++,
                type: OverlayType.TEXT,
                content: `Scene ${i+1} Visual: ${scene.visualDescription}`,
                from: Math.round(currentTimeOffset / 1000 * fps),
                durationInFrames: durationFrames,
                row: 2,
                left: 100,
                top: 50,
                width: 800,
                height: 80,
                isDragging: false,
                rotation: 0,
                styles: {
                  fontSize: "1rem",
                  fontWeight: "500",
                  color: "#FFFFFF",
                  backgroundColor: "rgba(0, 0, 0, 0.7)",
                  fontFamily: "font-sans",
                  fontStyle: "normal",
                  textDecoration: "none",
                  lineHeight: "1.5",
                  textAlign: "left",
                  padding: "10px",
                  borderRadius: "4px",
                  opacity: 0.8,
                  zIndex: 2
                }
              });
            }
          } else {
            // No videos found, use text description
            newOverlays.push({
              id: nextId++,
              type: OverlayType.TEXT,
              content: `Scene ${i+1} Visual: ${scene.visualDescription}`,
              from: Math.round(currentTimeOffset / 1000 * fps),
              durationInFrames: durationFrames,
              row: 2,
              left: 100,
              top: 50,
              width: 800,
              height: 80,
              isDragging: false,
              rotation: 0,
              styles: {
                fontSize: "1rem",
                fontWeight: "500",
                color: "#FFFFFF",
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                fontFamily: "font-sans",
                fontStyle: "normal",
                textDecoration: "none",
                lineHeight: "1.5",
                textAlign: "left",
                padding: "10px",
                borderRadius: "4px",
                opacity: 0.8,
                zIndex: 2
              }
            });
          }
        } else if (scene.visualType === "AI") {
          // Generate an image using the AI endpoint, then animate it
          try {
            // First, generate the image using the new Langfuse endpoint
            const imageGenResponse = await fetch('/api/ai/generate-image-langfuse', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                image_prompt: scene.visualDescription,
                aspect_ratio: "16:9",
                company_id: "default" // TODO: Get actual company_id from context
              }),
            });
            
            if (!imageGenResponse.ok) {
              throw new Error(`Failed to generate image: ${imageGenResponse.status}`);
            }
            
            const imageData = await imageGenResponse.json();
            
            if (!imageData.url) {
              throw new Error("No image URL returned from generate-image endpoint");
            }
            
            // Now animate this image using the animate-image endpoint
            const animateResponse = await fetch('/api/latest/animate-image', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                imageUrl: imageData.url,
                prompt: scene.visualDescription
              }),
            });
            
            if (!animateResponse.ok) {
              throw new Error(`Failed to initiate animation: ${animateResponse.status}`);
            }
            
            const animateData = await animateResponse.json();
            
            if (animateData.taskId) {
              // Poll for animation completion
              let animationComplete = false;
              let videoUrl = null;
              let retries = 0;
              const maxRetries = 30; // Maximum number of retries
              
              while (!animationComplete && retries < maxRetries) {
                retries++;
                
                // Wait a few seconds between polls
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                const statusResponse = await fetch('/api/latest/animate-image', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    taskId: animateData.taskId
                  }),
                });
                
                if (statusResponse.headers.get('Content-Type')?.includes('video/mp4')) {
                  // We got a video, download it and use it
                  const videoBlob = await statusResponse.blob();
                  videoUrl = URL.createObjectURL(videoBlob);
                  animationComplete = true;
                } else {
                  // Still processing or error
                  const statusData = await statusResponse.json();
                  if (statusData.status === 'FAILED') {
                    throw new Error('Animation failed');
                  }
                  if (statusData.status !== 'RUNNING') {
                    // Unexpected status
                    animationComplete = true;
                  }
                }
              }
              
              if (videoUrl) {
                // Add video overlay with the animated result
                newOverlays.push({
                  id: nextId++,
                  type: OverlayType.VIDEO,
                  content: scene.visualDescription, // Description as content
                  src: videoUrl, // Animated video URL
                  from: Math.round(currentTimeOffset / 1000 * fps), // Convert ms to frames
                  durationInFrames: durationFrames,
                  videoStartTime: 0,
                  row: 2,
                  left: 0,
                  top: 0,
                  width: 1920,
                  height: 1080,
                  isDragging: false,
                  rotation: 0,
                  styles: {
                    opacity: 1,
                    zIndex: 100,
                    transform: "none",
                    objectFit: "cover"
                  }
                });
              } else {
                // Fallback to text if animation wasn't completed
                throw new Error('Animation timed out');
              }
            } else {
              throw new Error('No task ID returned from animate-image endpoint');
            }
          } catch (error) {
            console.error('AI animation error:', error);
            // Fallback to text overlay if anything fails
            newOverlays.push({
              id: nextId++,
              type: OverlayType.TEXT,
              content: `AI Visual: ${scene.visualDescription} (Animation failed)`,
              from: Math.round(currentTimeOffset / 1000 * fps),
              durationInFrames: durationFrames,
              row: 2,
              left: 100,
              top: 50,
              width: 800,
              height: 80,
              isDragging: false,
              rotation: 0,
              styles: {
                fontSize: "1rem",
                fontWeight: "500",
                color: "#FFFFFF",
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                fontFamily: "font-sans",
                fontStyle: "normal",
                textDecoration: "none",
                lineHeight: "1.5",
                textAlign: "left",
                padding: "10px",
                borderRadius: "4px",
                opacity: 0.8,
                zIndex: 2
              }
            });
          }
        }
        
        // Update the offset for the next scene
        currentTimeOffset += durationMs;
      }
      
      // Create caption overlay containing all segments
      if (captionSegments.length > 0) {
        // Apply the selected font style
        let fontFamily = "Inter, sans-serif";
        let fontSize = "2.2rem";
        let fontWeight = 500;
        
        if (fontStyle === "Bold & modern") {
          fontFamily = "Montserrat, sans-serif";
          fontSize = "2.4rem";
          fontWeight = 700;
        } else if (fontStyle === "Clean & minimal") {
          fontFamily = "Inter, sans-serif";
          fontSize = "2.2rem";
          fontWeight = 400;
        } else if (fontStyle === "Playful & casual") {
          fontFamily = "Quicksand, sans-serif";
          fontSize = "2.4rem";
          fontWeight = 500;
        }
        
        newOverlays.push({
          id: nextId++,
          type: OverlayType.CAPTION,
          captions: captionSegments,
          from: 0,
          durationInFrames: Math.ceil(currentTimeOffset / 1000 * fps), // Convert ms to frames
          row: 1,
          left: 240,
          top: 500, // Position near bottom of screen
          width: 800, // Default width
          height: 150, // Default height for captions
          isDragging: false,
          rotation: 0,
          styles: {
            fontFamily,
            fontSize,
            lineHeight: 1.4,
            textAlign: "center",
            color: "#FFFFFF",
            textShadow: "2px 2px 4px rgba(0,0,0,0.6), 0 0 20px rgba(0,0,0,0.3)",
            padding: "12px",
            fontWeight,
            letterSpacing: "0.01em",
            highlightStyle: {
              backgroundColor: "rgba(59, 130, 246, 0.92)",
              color: "#FFFFFF",
              scale: 1.06,
              fontWeight: 600,
              textShadow: "2px 2px 4px rgba(0,0,0,0.4)",
              borderRadius: "6px",
              padding: "2px 8px",
            }
          },
          template: "classic"
        });
      }
      
      // Set all overlays at once to ensure they appear on the timeline
      setOverlays(newOverlays);
      
      setError(null);
      setActiveTabKey("scenes");
      
      // Show success message
      setTimeout(() => {
        setSuccess("Video generated successfully! You can now see audio, videos, and captions on the timeline.");
      }, 500);
      
    } catch (err: any) {
      setError(`Failed to generate video: ${err.message}`);
      console.error(err);
    } finally {
      setGeneratingVideo(false);
    }
  };
  
  /**
   * Extracts word-level timestamps from the Eleven Labs alignment data
   */
  const extractWordTimestamps = (ttsData: any, text: string) => {
    // If we have normalized alignment data (preferred), use that
    const alignmentData = ttsData.normalized_alignment || ttsData.alignment;
    
    if (!alignmentData || !alignmentData.characters) {
      // Fallback if no alignment data is available
      return text.split(/\s+/).map((word, index, arr) => ({
        word,
        start: (ttsData.duration / arr.length) * index,
        end: (ttsData.duration / arr.length) * (index + 1)
      }));
    }
    
    const { characters, character_start_times_seconds, character_end_times_seconds } = alignmentData;
    
    // Process character-level timestamps to get word-level timestamps
    const words = [];
    let currentWord = '';
    let wordStart = 0;
    
    for (let i = 0; i < characters.length; i++) {
      const char = characters[i];
      
      // If this is the first character of a word, mark the start time
      if (currentWord === '') {
        wordStart = character_start_times_seconds[i];
      }
      
      // If we're at a word boundary (space or punctuation) or at the end
      if (char === ' ' || char === '.' || char === ',' || char === '!' || 
          char === '?' || i === characters.length - 1) {
        
        // If we have accumulated a word, add it to timestamps
        if (currentWord.trim()) {
          words.push({
            word: currentWord.trim(),
            start: wordStart,
            end: character_end_times_seconds[i]
          });
        }
        
        // Reset for next word
        currentWord = '';
      } else {
        currentWord += char;
      }
    }
    
    return words;
  };

  return (
    <div className="flex flex-col h-full p-2 gap-2">
      {error && (
        <div className="text-red-500 text-sm p-2 mb-2 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-md">
          {error}
        </div>
      )}
      {success && (
        <div className="text-green-500 text-sm p-2 mb-2 bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-md">
          {success}
        </div>
      )}
      
      <Tabs value={activeTabKey} onValueChange={setActiveTabKey} className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="generate">Generate</TabsTrigger>
          <TabsTrigger value="scenes">Scenes ({generatedScenes.length})</TabsTrigger>
        </TabsList>
        <TabsContent value="generate" className="mt-2">
          <div className="flex flex-col gap-4">
            <div>
              <Label htmlFor="language">Language</Label>
              <Select 
                value={language} 
                onValueChange={setLanguage}
              >
                <SelectTrigger id="language">
                  <SelectValue placeholder="Select Language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="English">English</SelectItem>
                  <SelectItem value="Arabic">Arabic</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex flex-col gap-3 border p-3 rounded-md bg-slate-50 dark:bg-slate-900">
              <h3 className="text-md font-medium">🎬 What&apos;s the style of this promo?</h3>
              <div className="grid grid-cols-1 gap-2">
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="promoStyle" 
                    value="Inspiring brand story" 
                    checked={promoStyle === "Inspiring brand story"}
                    onChange={e => setPromoStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Inspiring brand story</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="promoStyle" 
                    value="Fast-paced product launch" 
                    checked={promoStyle === "Fast-paced product launch"}
                    onChange={e => setPromoStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Fast-paced product launch</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="promoStyle" 
                    value="Futuristic tech showcase" 
                    checked={promoStyle === "Futuristic tech showcase"}
                    onChange={e => setPromoStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Futuristic tech showcase</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="promoStyle" 
                    value="Clean & corporate" 
                    checked={promoStyle === "Clean & corporate"}
                    onChange={e => setPromoStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Clean & corporate</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="promoStyle" 
                    value="Edgy / startup vibe" 
                    checked={promoStyle === "Edgy / startup vibe"}
                    onChange={e => setPromoStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Edgy / startup vibe</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="promoStyle" 
                    value="AI choice" 
                    checked={promoStyle === "AI choice"}
                    onChange={e => setPromoStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>You decide (AI chooses based on blog)</span>
                </Label>
              </div>
            </div>
            
            <div className="flex flex-col gap-3 border p-3 rounded-md bg-slate-50 dark:bg-slate-900">
              <h3 className="text-md font-medium">🎨 Pick a tone (optional — choose up to 2):</h3>
              <div className="grid grid-cols-2 gap-2">
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="checkbox" 
                    value="Futuristic"
                    checked={toneSelections.includes("Futuristic")}
                    onChange={e => {
                      if (e.target.checked) {
                        if (toneSelections.length < 2) {
                          setToneSelections([...toneSelections, e.target.value]);
                        }
                      } else {
                        setToneSelections(toneSelections.filter(tone => tone !== e.target.value));
                      }
                    }}
                    className="h-4 w-4"
                  />
                  <span>Futuristic</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="checkbox" 
                    value="Inspiring"
                    checked={toneSelections.includes("Inspiring")}
                    onChange={e => {
                      if (e.target.checked) {
                        if (toneSelections.length < 2) {
                          setToneSelections([...toneSelections, e.target.value]);
                        }
                      } else {
                        setToneSelections(toneSelections.filter(tone => tone !== e.target.value));
                      }
                    }}
                    className="h-4 w-4"
                  />
                  <span>Inspiring</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="checkbox" 
                    value="Urgent"
                    checked={toneSelections.includes("Urgent")}
                    onChange={e => {
                      if (e.target.checked) {
                        if (toneSelections.length < 2) {
                          setToneSelections([...toneSelections, e.target.value]);
                        }
                      } else {
                        setToneSelections(toneSelections.filter(tone => tone !== e.target.value));
                      }
                    }}
                    className="h-4 w-4"
                  />
                  <span>Urgent</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="checkbox" 
                    value="Corporate"
                    checked={toneSelections.includes("Corporate")}
                    onChange={e => {
                      if (e.target.checked) {
                        if (toneSelections.length < 2) {
                          setToneSelections([...toneSelections, e.target.value]);
                        }
                      } else {
                        setToneSelections(toneSelections.filter(tone => tone !== e.target.value));
                      }
                    }}
                    className="h-4 w-4"
                  />
                  <span>Corporate</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="checkbox" 
                    value="Visionary"
                    checked={toneSelections.includes("Visionary")}
                    onChange={e => {
                      if (e.target.checked) {
                        if (toneSelections.length < 2) {
                          setToneSelections([...toneSelections, e.target.value]);
                        }
                      } else {
                        setToneSelections(toneSelections.filter(tone => tone !== e.target.value));
                      }
                    }}
                    className="h-4 w-4"
                  />
                  <span>Visionary</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="checkbox" 
                    value="Problem-solving"
                    checked={toneSelections.includes("Problem-solving")}
                    onChange={e => {
                      if (e.target.checked) {
                        if (toneSelections.length < 2) {
                          setToneSelections([...toneSelections, e.target.value]);
                        }
                      } else {
                        setToneSelections(toneSelections.filter(tone => tone !== e.target.value));
                      }
                    }}
                    className="h-4 w-4"
                  />
                  <span>Problem-solving</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="checkbox" 
                    value="Sustainability-focused"
                    checked={toneSelections.includes("Sustainability-focused")}
                    onChange={e => {
                      if (e.target.checked) {
                        if (toneSelections.length < 2) {
                          setToneSelections([...toneSelections, e.target.value]);
                        }
                      } else {
                        setToneSelections(toneSelections.filter(tone => tone !== e.target.value));
                      }
                    }}
                    className="h-4 w-4"
                  />
                  <span>Sustainability-focused</span>
                </Label>
              </div>
            </div>
            
            <div className="flex flex-col gap-3 border p-3 rounded-md bg-slate-50 dark:bg-slate-900">
              <h3 className="text-md font-medium">🎞️ How long should the video be?</h3>
              <div className="grid grid-cols-1 gap-2">
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="videoLength" 
                    value="Short (15–30 sec)"
                    checked={videoLength === "Short (15–30 sec)"}
                    onChange={e => setVideoLength(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Short (15–30 sec)</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="videoLength" 
                    value="Medium (30–60 sec)"
                    checked={videoLength === "Medium (30–60 sec)"}
                    onChange={e => setVideoLength(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Medium (30–60 sec)</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="videoLength" 
                    value="Full promo (60–90 sec)"
                    checked={videoLength === "Full promo (60–90 sec)"}
                    onChange={e => setVideoLength(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Full promo (60–90 sec)</span>
                </Label>
              </div>
            </div>
            
            <div className="flex flex-col gap-3 border p-3 rounded-md bg-slate-50 dark:bg-slate-900">
              <h3 className="text-md font-medium">🎵 Music Style (optional):</h3>
              <div className="grid grid-cols-1 gap-2">
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="musicStyle" 
                    value="Calm ambient"
                    checked={musicStyle === "Calm ambient"}
                    onChange={e => setMusicStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Calm ambient</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="musicStyle" 
                    value="Epic orchestral"
                    checked={musicStyle === "Epic orchestral"}
                    onChange={e => setMusicStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Epic orchestral</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="musicStyle" 
                    value="Futuristic synth"
                    checked={musicStyle === "Futuristic synth"}
                    onChange={e => setMusicStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Futuristic synth</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="musicStyle" 
                    value="Corporate piano"
                    checked={musicStyle === "Corporate piano"}
                    onChange={e => setMusicStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Corporate piano</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="musicStyle" 
                    value="None"
                    checked={musicStyle === "None"}
                    onChange={e => setMusicStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>None / I will add later</span>
                </Label>
              </div>
            </div>
            
            <div className="flex flex-col gap-3 border p-3 rounded-md bg-slate-50 dark:bg-slate-900">
              <h3 className="text-md font-medium">🔤 Font style for text overlays:</h3>
              <div className="grid grid-cols-1 gap-2">
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="fontStyle" 
                    value="Bold & modern"
                    checked={fontStyle === "Bold & modern"}
                    onChange={e => setFontStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Bold & modern</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="fontStyle" 
                    value="Clean & minimal"
                    checked={fontStyle === "Clean & minimal"}
                    onChange={e => setFontStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Clean & minimal</span>
                </Label>
                <Label className="flex items-center space-x-2 cursor-pointer">
                  <input 
                    type="radio" 
                    name="fontStyle" 
                    value="Playful & casual"
                    checked={fontStyle === "Playful & casual"}
                    onChange={e => setFontStyle(e.target.value)}
                    className="h-4 w-4"
                  />
                  <span>Playful & casual</span>
                </Label>
              </div>
            </div>
            
            <div className="flex flex-col gap-2">
              <Label htmlFor="article">Paste your article or text</Label>
              <Textarea
                id="article"
                placeholder="Paste your article or text here..."
                className="min-h-32 resize-none"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
              />
            </div>
            
            <Button 
              onClick={generateScript} 
              disabled={isLoading || !inputText.trim() || !promoStyle || !videoLength || !fontStyle}
              className="w-full"
            >
              {isLoading ? <Spinner className="mr-2" /> : null}
              {isLoading ? "Generating..." : "Generate Script"}
            </Button>
          </div>
        </TabsContent>
        
        <TabsContent value="scenes" className="mt-2">
          <ScrollArea className="h-[calc(100vh-240px)]">
            {generatedScenes.length > 0 ? (
              <div className="flex flex-col gap-4">
                {generatedScenes.map((scene, index) => (
                  <Collapsible key={index} className="border rounded-md">
                    <Card>
                      <CollapsibleTrigger className="w-full text-left p-4 flex justify-between items-center hover:bg-muted/30">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-sm">{`Scene ${index + 1}`}</span>
                        </div>
                        <ChevronDown className="h-4 w-4" />
                      </CollapsibleTrigger>
                      
                      <CollapsibleContent>
                        <CardContent className="p-4 pt-2">
                          {editingSceneIndex === index ? (
                            // Editing mode
                            <div className="flex flex-col gap-3">
                              <div>
                                <Label htmlFor={`scene-text-${index}`} className="text-xs text-muted-foreground mb-1 block">
                                  Text:
                                </Label>
                                <Textarea
                                  id={`scene-text-${index}`}
                                  value={editingScene?.text}
                                  onChange={(e) => setEditingScene({...editingScene!, text: e.target.value})}
                                  className="w-full min-h-20"
                                />
                              </div>
                              
                              <div>
                                <Label htmlFor={`visual-type-${index}`} className="text-xs text-muted-foreground mb-1 block">
                                  Visual Type:
                                </Label>
                                <Select
                                  value={editingScene?.visualType}
                                  onValueChange={(value) => setEditingScene({...editingScene!, visualType: value as "STOCK" | "AI"})}
                                >
                                  <SelectTrigger id={`visual-type-${index}`}>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="STOCK">STOCK</SelectItem>
                                    <SelectItem value="AI">AI</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <div>
                                <Label htmlFor={`visual-desc-${index}`} className="text-xs text-muted-foreground mb-1 block">
                                  Visual Description:
                                </Label>
                                <Textarea
                                  id={`visual-desc-${index}`}
                                  value={editingScene?.visualDescription}
                                  onChange={(e) => setEditingScene({...editingScene!, visualDescription: e.target.value})}
                                  className="w-full min-h-20"
                                />
                              </div>
                            </div>
                          ) : (
                            // View mode
                            <div className="flex flex-col gap-3">
                              <div>
                                <Label className="text-xs text-muted-foreground">Text:</Label>
                                <p className="font-medium">{scene.text}</p>
                              </div>
                              <div>
                                <Label className="text-xs text-muted-foreground">Visual Type:</Label>
                                <p>{scene.visualType}</p>
                              </div>
                              <div>
                                <Label className="text-xs text-muted-foreground">Visual Description:</Label>
                                <p className="text-sm">{scene.visualDescription}</p>
                              </div>
                            </div>
                          )}
                        </CardContent>
                        
                        <CardFooter className="px-4 pb-4 pt-0 flex justify-between">
                          {editingSceneIndex === index ? (
                            <>
                              <Button variant="outline" size="sm" onClick={cancelEditingScene}>
                                Cancel
                              </Button>
                              <Button size="sm" onClick={saveEditedScene}>
                                <Save className="h-4 w-4 mr-1" /> Save
                              </Button>
                            </>
                          ) : (
                            <>
                              <Button variant="destructive" size="sm" onClick={() => deleteScene(index)}>
                                <Trash2 className="h-4 w-4 mr-1" /> Delete
                              </Button>
                              <Button variant="outline" size="sm" onClick={() => startEditingScene(index)}>
                                <Edit className="h-4 w-4 mr-1" /> Edit
                              </Button>
                            </>
                          )}
                        </CardFooter>
                      </CollapsibleContent>
                    </Card>
                  </Collapsible>
                ))}
                
                <div className="flex flex-col gap-2 mt-2">
                  <Button onClick={addNewScene} variant="outline">
                    <Plus className="h-4 w-4 mr-1" /> Add New Scene
                  </Button>
                  
                  <Button 
                    onClick={generateVideo}
                    variant="default"
                    className="w-full"
                    disabled={generatingVideo || generatedScenes.length === 0}
                  >
                    {generatingVideo ? <Spinner className="mr-2" /> : <Video className="h-4 w-4 mr-1" />}
                    {generatingVideo ? "Generating..." : "Generate Video"}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center p-4 text-muted-foreground">
                {isLoading ? "Generating script..." : (
                  <div className="flex flex-col gap-4 items-center">
                    <p>No scenes generated yet. Go to the Generate tab to create a script.</p>
                    <div className="flex flex-col gap-2 w-full max-w-[300px]">
                      <Button onClick={addNewScene} variant="outline">
                        <Plus className="h-4 w-4 mr-1" /> Add Scene Manually
                      </Button>
                      <Button 
                        onClick={generateVideo}
                        variant="default"
                        disabled={generatingVideo || generatedScenes.length === 0}
                      >
                        <Video className="h-4 w-4 mr-1" /> Generate Video
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
}
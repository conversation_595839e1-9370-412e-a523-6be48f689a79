import { NextRequest, NextResponse } from 'next/server';

interface GenerateImageRequest {
  image_prompt: string;
  aspect_ratio: string;
  company_id: string;
  image_gen_styles?: any;
  brand_context?: string;
}

interface GenerateImageResponse {
  url: string;
  path?: string;
}

/**
 * POST handler for generating images using sb-server with Langfuse + OpenRouter
 * @description Routes image generation through sb-server using Langfuse prompts and Gemini 2.5 Flash Image Preview
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: GenerateImageRequest = await request.json();
    
    // Validate required fields
    if (!body.image_prompt || !body.company_id) {
      return NextResponse.json(
        { error: 'Missing required fields: image_prompt and company_id are required' }, 
        { status: 400 }
      );
    }

    // Call sb-server endpoint
    const sbServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
    const response = await fetch(`${sbServerUrl}/generate-image-with-langfuse`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image_prompt: body.image_prompt,
        aspect_ratio: body.aspect_ratio || 'custom',
        company_id: body.company_id,
        image_gen_styles: body.image_gen_styles,
        brand_context: body.brand_context
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`sb-server request failed with status ${response.status}: ${errorData.message || 'Unknown error'}`);
    }

    const data = await response.json();
    
    // Return in the expected format for compatibility
    const result: GenerateImageResponse = {
      url: data.url,
      path: data.path
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('Image generation error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to generate image';
    
    return NextResponse.json(
      { error: errorMessage }, 
      { status: 500 }
    );
  }
}

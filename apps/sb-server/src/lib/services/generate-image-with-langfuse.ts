import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import crypto from 'crypto';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

// Types for image generation
interface ImageGenerationParams {
  image_prompt: string;
  aspect_ratio: string;
  company_id: string;
  image_gen_styles?: any;
  brand_context?: string;
}

interface ImageGenerationResponse {
  url: string;
  path?: string;
  metadata?: {
    model: string;
    timestamp: string;
    company_id: string;
    prompt_hash: string;
  };
}

// Supported image generation models (actual image generation models)
const IMAGE_GENERATION_MODEL = "google/gemini-2.5-flash-image-preview"

/**
 * Generates an image using Langfuse prompts and OpenRouter with proper image generation models
 */
export async function generateImageWithLangfuse(params: ImageGenerationParams): Promise<ImageGenerationResponse> {
  try {
    const { image_prompt, aspect_ratio, company_id, image_gen_styles, brand_context } = params;

    // Validate required fields
    if (!image_prompt) {
      throw new Error('Missing required field: image_prompt');
    }

    if (!company_id) {
      throw new Error('Missing required field: company_id');
    }

    // Get Langfuse prompt template
    const prompt = await langfuse.getPrompt("generate_image_v1", undefined, { label: "production" });

    // Prepare template variables
    const templateVariables = {
      image_prompt,
      brand_brief: brand_context || "No specific brand guidelines provided",
      image_gen_styles: image_gen_styles ? JSON.stringify(image_gen_styles) : "Default professional style",
      aspect_ratio: aspect_ratio || "custom"
    };

    // Compile the prompt
    const compiledPrompt = prompt.compile(templateVariables);
    console.log("Generated image prompt:", compiledPrompt);

    // Generate image using OpenRouter API directly
    const imageData = await generateImageWithOpenRouter(compiledPrompt);

    // Generate metadata
    const promptHash = crypto.createHash('md5').update(compiledPrompt).digest('hex');
    const metadata = {
      model: IMAGE_GENERATION_MODEL,
      timestamp: new Date().toISOString(),
      company_id,
      prompt_hash: promptHash
    };

    // Upload image and return response
    const uploadResult = await uploadImage(imageData, metadata);

    return {
      ...uploadResult,
      metadata
    };

  } catch (error) {
    console.error('Error generating image with Langfuse:', error);
    throw new Error(`Failed to generate image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate image using OpenRouter API with Gemini 2.5 Flash Image Preview
 */
async function generateImageWithOpenRouter(prompt: string): Promise<Buffer> {
  try {
    console.log('Generating image with OpenRouter using prompt:', prompt);

    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }

    // OpenRouter API call for image generation
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.OPENROUTER_REFERER || 'https://smartberry.ai',
        'X-Title': 'SmartBerry Image Generation'
      },
      body: JSON.stringify({
        model: IMAGE_GENERATION_MODEL,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1024,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('OpenRouter API error:', errorText);
      throw new Error(`OpenRouter API request failed with status ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('OpenRouter response:', JSON.stringify(data, null, 2));

    // Extract image data from response
    const messageContent = data.choices?.[0]?.message?.content;
    if (!messageContent) {
      throw new Error('No content in OpenRouter response');
    }

    // For Gemini 2.5 Flash Image Preview, the response should contain base64 image data
    // The exact format may vary, so we need to handle different possible formats
    let base64Data: string;

    if (typeof messageContent === 'string') {
      // If it's a direct base64 string
      base64Data = messageContent;
    } else if (messageContent.image_url) {
      // If it contains an image URL with base64 data
      base64Data = messageContent.image_url.replace(/^data:image\/[^;]+;base64,/, '');
    } else {
      throw new Error('Unexpected response format from OpenRouter');
    }

    // Clean up the base64 data
    base64Data = base64Data.replace(/\s/g, '');

    // Validate base64 format
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(base64Data)) {
      console.log('Invalid base64 data received:', base64Data.substring(0, 100));
      throw new Error('Invalid base64 format in OpenRouter response');
    }

    // Convert to buffer
    const imageBuffer = Buffer.from(base64Data, 'base64');
    console.log('Successfully generated image, buffer size:', imageBuffer.length);

    return imageBuffer;

  } catch (error) {
    console.error('Error generating image with OpenRouter:', error);
    throw new Error(`Failed to generate image with OpenRouter: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to storage and return URL
 * Uses ImgBB for development and Supabase for production
 */
async function uploadImage(imageBuffer: Buffer, metadata: any): Promise<ImageGenerationResponse> {
  try {
    console.log('Uploading image to storage, buffer size:', imageBuffer.length);

    // Determine storage strategy based on environment
    const isProduction = process.env.NODE_ENV === 'production';

    if (isProduction && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      // Use Supabase storage for production
      const result = await uploadToSupabase(imageBuffer, metadata.company_id);
      return {
        url: result.url,
        path: result.path,
        metadata
      };
    } else {
      // Use ImgBB for development
      const result = await uploadToImgBB(imageBuffer);
      return {
        url: result.url,
        path: result.url, // For compatibility
        metadata
      };
    }

  } catch (error) {
    console.error('Error uploading image:', error);
    throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to Supabase Storage (production environment)
 */
async function uploadToSupabase(imageBuffer: Buffer, companyId: string): Promise<{ url: string; path: string }> {
  try {
    // Use require for CommonJS compatibility in Node.js
    const { createClient } = require('@supabase/supabase-js');

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required');
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const uniqueId = crypto.randomUUID();
    const fileName = `${uniqueId}-${timestamp}.png`;
    const filePath = `${companyId}/generated/${fileName}`;

    console.log('Uploading to Supabase storage, path:', filePath);

    // Upload to Supabase storage
    const { error: uploadError } = await supabase.storage
      .from('generated')
      .upload(filePath, imageBuffer, {
        contentType: 'image/png',
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      console.error('Supabase upload error:', uploadError);
      throw uploadError;
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('generated')
      .getPublicUrl(filePath);

    console.log('Successfully uploaded to Supabase:', publicUrl);
    return { url: publicUrl, path: filePath };

  } catch (error) {
    console.error('Error uploading to Supabase:', error);
    throw new Error(`Failed to upload to Supabase: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to ImgBB (development environment)
 */
async function uploadToImgBB(imageBuffer: Buffer): Promise<{ url: string }> {
  const imgbbApiKey = process.env.IMGBB_API_KEY;

  if (!imgbbApiKey) {
    throw new Error('IMGBB_API_KEY environment variable is required');
  }

  // Convert buffer to base64 string
  const base64Image = imageBuffer.toString('base64');

  // Create form data using URLSearchParams for Node.js compatibility
  const formData = new URLSearchParams();
  formData.append('image', base64Image);

  console.log('Uploading to ImgBB with base64 length:', base64Image.length);

  const imgbbRes = await fetch(`https://api.imgbb.com/1/upload?key=${imgbbApiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: formData,
  });

  if (!imgbbRes.ok) {
    const errorText = await imgbbRes.text();
    console.log('ImgBB error response:', errorText);
    throw new Error(`ImgBB upload failed with status ${imgbbRes.status}: ${errorText}`);
  }

  const imgbbData = await imgbbRes.json();
  console.log('ImgBB response:', imgbbData);

  if (!imgbbData.success) {
    throw new Error(`ImgBB upload reported failure: ${JSON.stringify(imgbbData)}`);
  }

  return { url: imgbbData.data.url };
}


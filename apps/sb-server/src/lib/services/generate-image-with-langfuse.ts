import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM, LLM_MODEL_ID } from '../utils/callLLM.js';
import crypto from 'crypto';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

// Types for image generation
interface ImageGenerationParams {
  image_prompt: string;
  aspect_ratio: string;
  company_id: string;
  image_gen_styles?: any;
  brand_context?: string;s
}

interface ImageGenerationResponse {
  url: string;
  path?: string;
  metadata?: {
    model: string;
    timestamp: string;
    company_id: string;
    prompt_hash: string;
  };
}

// Supported image generation models (actual image generation models)
const IMAGE_GENERATION_MODEL = "google/gemini-2.5-flash-image-preview"

/**
 * Generates an image using <PERSON><PERSON> prompts and OpenRouter with proper image generation models
 */
export async function generateImageWithLangfuse(params: ImageGenerationParams): Promise<ImageGenerationResponse> {
  try {
    const { image_prompt, aspect_ratio, company_id, image_gen_styles, brand_context } = params;

    // Validate required fields
    if (!image_prompt) {
      throw new Error('Missing required field: image_prompt');
    }

    if (!company_id) {
      throw new Error('Missing required field: company_id');
    }

    // Get Langfuse prompt template
    const prompt = await langfuse.getPrompt("generate_image_v1", undefined, { label: "production" });
    
    // Prepare template variables
    const templateVariables = {
      image_prompt,
      brand_brief: brand_context || "No specific brand guidelines provided",
      image_gen_styles: image_gen_styles ? JSON.stringify(image_gen_styles) : "Default professional style",
      aspect_ratio: aspect_ratio || "custom"
    };

    // Compile the prompt
    const compiledPrompt = prompt.compile(templateVariables);
    console.log("Generated image prompt:", compiledPrompt);
    
    const langfuseConfig = {
        llm_model_id: IMAGE_GENERATION_MODEL,
        temperature: (prompt.config as any)?.temperature || 0.7,
        max_tokens: (prompt.config as any)?.max_tokens || 1024,
        ...(prompt.config && typeof prompt.config === 'object' ? prompt.config : {})
      };
    
    const response = await callLLM(
      compiledPrompt,
      langfuseConfig,
      IMAGE_GENERATION_MODEL,
      { parse: false }, // Don't parse as JSON for image generation
      []
    );
    
    console.log('Raw response from callLLM:', response);
    console.log('Response type:', typeof response);
    
    if (!response) {
      throw new Error(`Image generation failed with model: ${IMAGE_GENERATION_MODEL} - no response received`);
    }
    
    console.log(`Successfully generated image with model: ${IMAGE_GENERATION_MODEL}`);
    
    // Process the response to extract image data
    const imageData = await processImageResponse(response, IMAGE_GENERATION_MODEL);
    
    // Generate metadata
    const promptHash = crypto.createHash('md5').update(compiledPrompt).digest('hex');
    const metadata = {
      model: IMAGE_GENERATION_MODEL,
      timestamp: new Date().toISOString(),
      company_id,
      prompt_hash: promptHash
    };

    // Upload image and return response
    const uploadResult = await uploadImage(imageData, metadata);
    
    return {
      ...uploadResult,
      metadata
    };

  } catch (error) {
    console.error('Error generating image with Langfuse:', error);
    throw new Error(`Failed to generate image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Process the image response from the LLM to extract base64 image data
 */
async function processImageResponse(response: any, model: string): Promise<Buffer> {
  try {
    console.log('Processing image response from model:', model);
    console.log('Response type:', typeof response);
    console.log('Response content:', JSON.stringify(response, null, 2));
    
    // Handle different response formats
    let imageData: string | null = null;
    
    // Check if response is a string (old format)
    if (typeof response === 'string') {
      imageData = response;
    }
    // Check if response is an object with images array (Langfuse format)
    else if (response && typeof response === 'object' && response.images && Array.isArray(response.images)) {
      const imageItem = response.images[0];
      if (imageItem && imageItem.image_url && imageItem.image_url.url) {
        const langfuseUrl = imageItem.image_url.url;
        console.log('Found Langfuse media URL:', langfuseUrl);
        
        // Extract image data from Langfuse media URL
        imageData = await extractImageFromLangfuseMedia(langfuseUrl);
      }
    }
    // Check if response is an object with content (direct format)
    else if (response && typeof response === 'object' && response.content) {
      imageData = response.content;
    }
    
    if (!imageData) {
      throw new Error('No image data found in response');
    }
    
    // Process the image data
    let base64Data = imageData;
    
    // Remove any potential data URL prefix if present
    if (base64Data.includes('data:image/')) {
      base64Data = base64Data.split(',')[1];
    }
    
    // Clean up any whitespace or newlines
    base64Data = base64Data.replace(/\s/g, '');
    
    // Validate base64 format
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(base64Data)) {
      console.log('Invalid base64 data:', base64Data.substring(0, 100));
      throw new Error('Invalid base64 format in response');
    }
    
    // Convert base64 to buffer
    const imageBuffer = Buffer.from(base64Data, 'base64');
    
    console.log('Successfully processed image, buffer size:', imageBuffer.length);
    return imageBuffer;
    
  } catch (error) {
    console.error('Error processing image response:', error);
    throw new Error(`Failed to process image response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Extract image data from Langfuse media URL using Langfuse client
 */
async function extractImageFromLangfuseMedia(langfuseUrl: string): Promise<string> {
  try {
    console.log('Extracting image from Langfuse media URL:', langfuseUrl);
    
    // Use Langfuse client to resolve media references
    const resolvedResponse = await langfuse.resolveMediaReferences({
      obj: { image: langfuseUrl },
      resolveWith: 'base64DataUri'
    });
    
    console.log('Resolved media response:', resolvedResponse);
    
    // Extract the base64 data URI from the resolved response
    if (resolvedResponse && resolvedResponse.image && typeof resolvedResponse.image === 'string') {
      return resolvedResponse.image;
    }
    
    throw new Error('Failed to resolve Langfuse media reference');
    
  } catch (error) {
    console.error('Error extracting image from Langfuse media:', error);
    throw error;
  }
}

/**
 * Upload image to storage and return URL
 */
async function uploadImage(imageBuffer: Buffer, metadata: any): Promise<ImageGenerationResponse> {
  try {
    console.log('Uploading image to storage, buffer size:', imageBuffer.length);
    
    // For now, use ImgBB as the storage solution
    const result = await uploadToImgBB(imageBuffer);
    
    return {
      url: result.url,
      path: result.url, // For compatibility
      metadata
    };
    
  } catch (error) {
    console.error('Error uploading image:', error);
    throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to ImgBB (development environment)
 */
async function uploadToImgBB(imageBuffer: Buffer): Promise<ImageGenerationResponse> {
  const imgbbApiKey = process.env.IMGBB_API_KEY;
  
  if (!imgbbApiKey) {
    throw new Error('IMGBB_API_KEY environment variable is required');
  }
  
  // Convert buffer to base64 string
  const base64Image = imageBuffer.toString('base64');
  
  // Create form data using URLSearchParams for Node.js compatibility
  const formData = new URLSearchParams();
  formData.append('image', base64Image);

  console.log('Uploading to ImgBB with base64 length:', base64Image.length);

  const imgbbRes = await fetch(`https://api.imgbb.com/1/upload?key=${imgbbApiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: formData,
  });

  if (!imgbbRes.ok) {
    const errorText = await imgbbRes.text();
    console.log('ImgBB error response:', errorText);
    throw new Error(`ImgBB upload failed with status ${imgbbRes.status}: ${errorText}`);
  }

  const imgbbData = await imgbbRes.json();
  console.log('ImgBB response:', imgbbData);

  if (!imgbbData.success) {
    throw new Error(`ImgBB upload reported failure: ${JSON.stringify(imgbbData)}`);
  }

  return { url: imgbbData.data.url };
}

